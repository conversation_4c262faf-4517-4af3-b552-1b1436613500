#!/usr/bin/env python3
"""
Test script to verify that alternativeCompletion is now optional
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_attempt_creation_without_alternative():
    """Test that attempts can be created without alternativeCompletion"""
    
    # Mock attempt data without alternativeCompletion
    attempt_without_alt = {
        'service_slug': 'TEST_SERVICE',
        'reservationRequest': 'test_request',
        'model': 'test_model',
        'buildingNumber': 'test_building',
        'floor': 'test_floor',
        'unitNumber': 'test_unit',
        'display': 'Test Display'
    }
    
    # Mock attempt data with alternativeCompletion
    attempt_with_alt = {
        'service_slug': 'TEST_SERVICE',
        'reservationRequest': 'test_request',
        'model': 'test_model',
        'buildingNumber': 'test_building',
        'floor': 'test_floor',
        'unitNumber': 'test_unit',
        'alternativeCompletion': 'test_completion',
        'display': 'Test Display'
    }
    
    print("Testing attempt without alternativeCompletion:")
    print(f"  Has alternativeCompletion: {'alternativeCompletion' in attempt_without_alt}")
    print(f"  Value: {attempt_without_alt.get('alternativeCompletion', 'Not set')}")
    
    print("\nTesting attempt with alternativeCompletion:")
    print(f"  Has alternativeCompletion: {'alternativeCompletion' in attempt_with_alt}")
    print(f"  Value: {attempt_with_alt.get('alternativeCompletion', 'Not set')}")
    
    # Test the logging logic
    print("\nTesting logging logic:")
    for i, attempt in enumerate([attempt_without_alt, attempt_with_alt], 1):
        print(f"\nAttempt {i}:")
        alt_completion = attempt.get("alternativeCompletion")
        if alt_completion:
            print(f'   • الاستكمال البديل: {alt_completion}')
        else:
            print('   • الاستكمال البديل: غير محدد (اختياري)')
    
    print("\n✅ All tests passed! alternativeCompletion is now optional.")

if __name__ == "__main__":
    test_attempt_creation_without_alternative()
